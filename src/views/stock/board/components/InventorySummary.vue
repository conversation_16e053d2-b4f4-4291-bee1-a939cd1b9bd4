<template>
  <div class="inventory-summary">
    <el-row :gutter="20" class="summary-row">
      <!-- 库存总量 -->
      <el-col :span="12">
        <div class="summary-card">
          <div class="card-title">库存总量</div>
          <div class="chart-container">
            <div id="inventoryChart" class="chart">
              <div v-if="!chartAvailable" class="chart-placeholder">
                <div class="placeholder-circle">
                  <div class="placeholder-inner">
                    <div class="placeholder-text">库存总量</div>
                    <div class="placeholder-value">{{ summaryData.totalCount || 192 }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="chart-data">
              <div class="data-item">
                <span class="data-label">入库总数</span>
                <span class="data-value">{{ summaryData.totalCount || 192 }} 条</span>
              </div>
              <div class="data-item">
                <span class="data-label">入库总重量</span>
                <span class="data-value">{{ summaryData.totalWeight || 12458 }} kg</span>
              </div>
              <div class="data-item">
                <span class="data-label">低库存预警</span>
                <span class="data-value warning">{{ summaryData.lowStockCount || 20 }} 条</span>
              </div>
              <div class="data-item">
                <span class="data-label">呆滞库存</span>
                <span class="data-value">{{ summaryData.stagnantCount || 18 }} 条</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      
      <!-- 批次分布 -->
      <el-col :span="12">
        <div class="summary-card">
          <div class="card-title">批次分布</div>
          <div class="chart-container">
            <div id="batchChart" class="chart">
              <!-- 备用显示，当ECharts不可用时 -->
              <div v-if="!chartAvailable" class="chart-placeholder">
                <div class="placeholder-pie">
                  <div class="pie-text">批次分布</div>
                </div>
              </div>
            </div>
            <div class="batch-legend">
              <div class="legend-item">
                <span class="legend-color" style="background: #165DFF;"></span>
                <span class="legend-label">近1个月入库</span>
                <span class="legend-value">{{ batchData.oneMonth || 45 }} %</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background: #00B42A;"></span>
                <span class="legend-label">近1-3个月入库</span>
                <span class="legend-value">{{ batchData.threeMonth || 25 }} %</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background: #FF7D00;"></span>
                <span class="legend-label">近3-6个月入库</span>
                <span class="legend-value">{{ batchData.sixMonth || 20 }} %</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background: #F53F3F;"></span>
                <span class="legend-label">近6个月以上</span>
                <span class="legend-value">{{ batchData.overSixMonth || 10 }} %</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'InventorySummary',
  props: {
    summaryData: {
      type: Object,
      default: () => ({})
    },
    batchData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      inventoryChart: null,
      batchChart: null,
      chartAvailable: false
    };
  },

  mounted() {
    // 检查ECharts是否可用
    const echartsInstance = echarts || this.$echarts;
    this.chartAvailable = !!echartsInstance;

    if (this.chartAvailable) {
      // 延迟初始化图表，确保DOM完全渲染
      setTimeout(() => {
        this.initInventoryChart();
        this.initBatchChart();
      }, 100);
    }
  },
  watch: {
    summaryData: {
      handler() {
        setTimeout(() => {
          this.initInventoryChart();
        }, 100);
      },
      deep: true
    },
    batchData: {
      handler() {
        setTimeout(() => {
          this.initBatchChart();
        }, 100);
      },
      deep: true
    }
  },
  beforeDestroy() {
    // 清理图表实例
    if (this.inventoryChart) {
      this.inventoryChart.dispose();
    }
    if (this.batchChart) {
      this.batchChart.dispose();
    }
  },
  methods: {
    initInventoryChart() {
      const chartDom = document.getElementById('inventoryChart');
      if (!chartDom) return;

      // 使用本地导入的echarts或全局的$echarts
      const echartsInstance = echarts || this.$echarts;
      if (!echartsInstance) {
        console.warn('ECharts not available');
        return;
      }

      // 如果已存在图表实例，先销毁
      if (this.inventoryChart) {
        this.inventoryChart.dispose();
      }

      try {
        this.inventoryChart = echartsInstance.init(chartDom);
        const option = {
          series: [{
            type: 'pie',
            radius: ['60%', '80%'],
            center: ['50%', '50%'],
            data: [
              { value: this.summaryData.totalCount || 192, name: '库存总量' }
            ],
            label: {
              show: false
            },
            itemStyle: {
              color: '#165DFF'
            },
            emphasis: {
              disabled: true
            }
          }]
        };
        this.inventoryChart.setOption(option);
      } catch (error) {
        console.error('初始化库存图表失败:', error);
      }
    },

    initBatchChart() {
      const chartDom = document.getElementById('batchChart');
      if (!chartDom) return;

      // 使用本地导入的echarts或全局的$echarts
      const echartsInstance = echarts || this.$echarts;
      if (!echartsInstance) {
        console.warn('ECharts not available');
        return;
      }

      // 如果已存在图表实例，先销毁
      if (this.batchChart) {
        this.batchChart.dispose();
      }

      try {
        this.batchChart = echartsInstance.init(chartDom);
        const option = {
          series: [{
            type: 'pie',
            radius: '70%',
            center: ['50%', '50%'],
            data: [
              { value: this.batchData.oneMonth || 45, name: '近1个月入库', itemStyle: { color: '#165DFF' } },
              { value: this.batchData.threeMonth || 25, name: '近1-3个月入库', itemStyle: { color: '#00B42A' } },
              { value: this.batchData.sixMonth || 20, name: '近3-6个月入库', itemStyle: { color: '#FF7D00' } },
              { value: this.batchData.overSixMonth || 10, name: '近6个月以上', itemStyle: { color: '#F53F3F' } }
            ],
            label: {
              show: false
            },
            emphasis: {
              disabled: true
            }
          }]
        };
        this.batchChart.setOption(option);
      } catch (error) {
        console.error('初始化批次图表失败:', error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.inventory-summary {
  .summary-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    height: 300px;
    
    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: #165DFF;
      margin-bottom: 20px;
    }
    
    .chart-container {
      display: flex;
      height: 240px;
      
      .chart {
        flex: 1;
        height: 100%;
        position: relative;

        .chart-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .placeholder-circle {
            width: 120px;
            height: 120px;
            border: 20px solid #165DFF;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            .placeholder-inner {
              text-align: center;

              .placeholder-text {
                font-size: 12px;
                color: #4E5969;
                margin-bottom: 4px;
              }

              .placeholder-value {
                font-size: 18px;
                font-weight: 500;
                color: #165DFF;
              }
            }
          }

          .placeholder-pie {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(45deg, #165DFF 25%, #00B42A 25% 50%, #FF7D00 50% 75%, #F53F3F 75%);
            display: flex;
            align-items: center;
            justify-content: center;

            .pie-text {
              background: white;
              padding: 8px 12px;
              border-radius: 4px;
              font-size: 12px;
              color: #4E5969;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
          }
        }
      }
      
      .chart-data {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 20px;
        padding-right: 80px;
        
        .data-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          
          .data-label {
            font-size: 14px;
            color: #4E5969;
          }
          
          .data-value {
            font-size: 16px;
            font-weight: 500;
            color: #165DFF;
            
            &.warning {
              color: #FF7D00;
            }
          }
        }
      }
      
      .batch-legend {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 20px;
        padding-right: 80px;
        
        .legend-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          
          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 12px;
          }
          
          .legend-label {
            flex: 1;
            font-size: 14px;
            color: #4E5969;
          }
          
          .legend-value {
            font-size: 16px;
            font-weight: 500;
            color: #1D2129;
          }
        }
      }
    }
  }
}
</style>
