<template>
  <div class="warehouse-distribution">
    <div class="distribution-header">
      <div class="title-section">
        <span class="title">仓库分布图</span>
        <div class="view-more-btn" v-if="warehouses.length > 8" @click="handleViewMore">
          <span>查看全部</span>
          <span class="count">{{ warehouses.length }}</span>
        </div>
      </div>
      <div class="legend">
        <div class="legend-item">
          <span class="legend-color high"></span>
          <span class="legend-text">高库存 (>80%)</span>
        </div>
        <div class="legend-item">
          <span class="legend-color medium"></span>
          <span class="legend-text">中等库存 (50-80%)</span>
        </div>
        <div class="legend-item">
          <span class="legend-color low"></span>
          <span class="legend-text">低库存 (<50%)</span>
        </div>
      </div>
    </div>
    
    <div class="warehouse-grid">
      <div 
        v-for="(warehouse, index) in displayWarehouses" 
        :key="warehouse.warehouseId"
        class="warehouse-card"
        :class="getWarehouseClass(warehouse.stockLevel)"
      >
        <div class="warehouse-number">{{ index + 1 }}</div>
        <div class="warehouse-name">{{ warehouse.warehouseName }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WarehouseDistribution',
  props: {
    warehouses: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showAll: false
    };
  },
  computed: {
    displayWarehouses() {
      return this.showAll ? this.warehouses : this.warehouses.slice(0, 8);
    }
  },
  methods: {
    handleViewMore() {
      this.showAll = !this.showAll;
      this.$emit('onViewMore', this.showAll);
    },
    getWarehouseClass(stockLevel) {
      if (stockLevel > 80) return 'high-stock';
      if (stockLevel >= 50) return 'medium-stock';
      return 'low-stock';
    }
  }
};
</script>

<style lang="scss" scoped>
.warehouse-distribution {
  .distribution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .title-section {
      display: flex;
      align-items: center;
      gap: 20px;
      
      .title {
        font-size: 16px;
        font-weight: 500;
        color: #1D2129;
      }
      
      .view-more-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        color: #165DFF;
        font-size: 14px;
        
        .count {
          background: #165DFF;
          color: white;
          padding: 2px 8px;
          border-radius: 10px;
          font-size: 12px;
          min-width: 20px;
          text-align: center;
        }
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
    
    .legend {
      display: flex;
      gap: 20px;
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
          
          &.high {
            background: #F53F3F;
          }
          
          &.medium {
            background: #FF7D00;
          }
          
          &.low {
            background: #00B42A;
          }
        }
        
        .legend-text {
          font-size: 14px;
          color: #4E5969;
        }
      }
    }
  }
  
  .warehouse-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 16px;
    
    .warehouse-card {
      position: relative;
      height: 120px;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &.high-stock {
        background: rgba(245, 63, 63, 0.1);
        border: 1px solid rgba(245, 63, 63, 0.3);
        
        &:hover {
          background: rgba(245, 63, 63, 0.15);
        }
      }
      
      &.medium-stock {
        background: rgba(255, 125, 0, 0.1);
        border: 1px solid rgba(255, 125, 0, 0.3);
        
        &:hover {
          background: rgba(255, 125, 0, 0.15);
        }
      }
      
      &.low-stock {
        background: rgba(0, 180, 42, 0.1);
        border: 1px solid rgba(0, 180, 42, 0.3);
        
        &:hover {
          background: rgba(0, 180, 42, 0.15);
        }
      }
      
      .warehouse-number {
        position: absolute;
        top: 8px;
        left: 8px;
        width: 20px;
        height: 20px;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 500;
      }
      
      .warehouse-name {
        font-size: 16px;
        font-weight: 500;
        color: #1D2129;
        text-align: center;
      }
    }
  }
}
</style>
