<template>
  <div class="warn-card">
    <!-- 低库存预警卡片 -->
    <el-card v-if="type === 'lowStock'" shadow="always" class="warning-card idle-material-card">
      <div class="card-content">
        <div class="card-header">
          <div class="warning-code">{{ data.code }}
            <span class="warning-date">
              {{ data.createTime }}
            </span>
          </div>
          <div class="warning-level-badge" :class="getLevelClass(data.level)">{{ data.levelText }}</div>
        </div>

        <div class="stock-section">
          <div class="stock-row">
            <div class="stock-item">
              <span class="stock-label">当前库存：</span>
              <span class="stock-value">{{ data.currentStock }}</span>
            </div>
            <div class="stock-item">
              <span class="stock-label">安全库存：</span>
              <span class="stock-value">{{ data.safeStock }}</span>
            </div>
            <div class="stock-item">
              <span class="stock-label" :class="data.currentStock < data.safeStock ? 'negative' : 'positive'">
                短缺量：
              </span>
              <span class="stock-diff stock-value">{{ data.warehouseInventorySubNum }}</span>
            </div>
          </div>

          <div class="progress-container">
            <el-progress :percentage="getStockPercentage(data)" :color="getProgressColor(data)" :show-text="false"
              :stroke-width="8"></el-progress>
          </div>
        </div>

        <div class="card-actions">
          <div>
            <el-tag :type="getActionStatus(data.warningStatus).type">
              {{ getActionStatus(data.warningStatus).text }}
            </el-tag>

          </div>
          <div class="action-buttons">
            <el-button v-for="action in data.actions" :key="action" size="mini"
              :type="action === '处理' ? 'primary' : 'default'" @click="handleAction(action, data)">
              {{ action }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
    <!-- 保质期 -->
    <el-card v-else-if="type === 'idleMat'" shadow="always" class="warning-card">
      <div class="card-content">
        <div class="card-left">
          <div class="warning-info">
            <div class="warning-code">{{ data.code }}</div>
            <div class="warning-meta">
              <span class="warning-time">预警时间：{{ data.createTime }}</span>
              <span class="warning-level" :class="getLevelClass(data.level)">{{ data.levelText }}</span>
            </div>
            <div class="warning-detail">
              <span class="detail-label">库存数量：</span>
              <span class="detail-value">{{ data.count }}</span>
            </div>
          </div>
        </div>
        <div class="card-right">
          <div class="action-buttons">
            <el-button v-for="action in data.actions" :key="action" size="mini"
              :type="action === '处理' ? 'primary' : 'default'" @click="handleAction(action, data)">
              {{ action }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
    <!-- 呆滞料预警卡片 -->
    <el-card v-else-if="type === 'expiry'" shadow="always" class="warning-card">
      <div class="card-content">
        <div class="card-left">
          <div class="warning-info">
            <div class="warning-header">
              <div class="warning-code">{{ data.code }}</div>
              <div class="batch-info">批次号：{{ data.batch }}</div>
            </div>
            <div class="warning-meta">
              <span class="warning-time">预警时间：{{ data.createTime }}</span>
              <span class="warning-level" :class="getLevelClass(data.level)">{{ data.levelText }}</span>
            </div>
            <div class="warning-detail">
              <span class="detail-label">保质期至：</span>
              <span class="detail-value">{{ data.expiryDate }}</span>
              <span class="status-tag" :class="getStatusClass(data.status)">{{ data.status }}</span>
            </div>
          </div>
        </div>
        <div class="card-right">
          <div class="action-buttons">
            <el-button v-for="action in data.actions" :key="action" size="mini"
              :type="action === '处理' ? 'primary' : 'default'" @click="handleAction(action, data)">
              {{ action }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>



  </div>
</template>

<script>

export default {
  name: 'WarnCard',
  props: {
    data: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      required: true
    }
  },
  methods: {
    // 获取等级样式类
    getLevelClass(level) {
      const classMap = {
        high: 'level-high',
        medium: 'level-medium',
        low: 'level-low'
      }
      return classMap[level] || ''
    },

    // 获取状态样式类
    getStatusClass(status) {
      if (status === '已过期') {
        return 'status-expired'
      } else if (status === '即将过期') {
        return 'status-expiring'
      }
      return ''
    },

    // 获取库存百分比
    getStockPercentage(data) {
      console.log('库存数量🚀🚀',data)
      if (!data.safeStock) return 0
      return Math.min((data.currentStock / data.safeStock) * 100, 100)
    },

    // 获取进度条颜色
    getProgressColor(data) {
      const percentage = this.getStockPercentage(data)
      if (percentage < 30) return '#F56C6C'
      if (percentage < 60) return '#E6A23C'
      return '#67C23A'
    },
    getActionStatus(status) {
      const statusMap = {
        1: {
          type: 'danger',
          text: '待处理'
        },
        2: {
          type: 'warning',
          text: '处理中'
        },
        3: {
          type: 'success',
          text: '已处理'
        }
      }
      return statusMap[status]
    },

    // 处理操作按钮点击
    handleAction(action, data) {
      const typeNames = {
        'lowStock': '低库存预警',
        'expiry': '保质期预警',
        'idleMat': '呆滞料预警'
      }

      const typeName = typeNames[this.type] || '未知预警'

      console.log(`${action} ${typeName}:`, {
        预警类型: typeName,
        预警ID: data.id,
        预警数据: data,
        操作: action
      })

      this.$message.success(`${action}${typeName}操作已执行`)
    }
  }
}
</script>

<style scoped lang='scss'>
@import '@/assets/css/theme.scss';

.warn-card {
  margin-top: 10px;

  .warning-card {
    border: 1px solid #EEEEEE;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-color: #D9D9D9;
    }

    ::v-deep .el-card__body {
      padding: 20px;
    }

    &.idle-material-card {
      .card-content {
        display: block;
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .warning-code {
          font-size: 16px;
          font-weight: 500;
          color: $title_text_color;

          .warning-date {
            margin-left: 100px;
          }
        }

        .warning-date {
          font-size: 14px;
          color: $value_color;
        }

        .warning-level-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;

          &.level-high {
            background: #FF4D4F;
            color: white;
          }

          &.level-medium {
            background: #FFC700;
            color: #333;
          }

          &.level-low {
            background: #52C41A;
            color: white;
          }
        }
      }

      .stock-section {
        margin-bottom: 16px;

        .stock-row {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 12px;

          .stock-label {
            font-size: 14px;
            color: $label_color;
          }

          .stock-value {
            font-size: 16px;
            color: $value_color;
            font-weight: 500;
          }

          .stock-diff {
            color: #FF4D4F !important;

            &.negative {
              color: #FF4D4F;
            }

            &.positive {
              color: #52C41A;
            }
          }

          .stock-item {
            width: 30%;

            .stock-label {
              font-size: 14px;
              color: $label_color;
            }

            .stock-value {
              font-size: 16px;
              color: $value_color;
              margin-left: 4px;
            }
          }
        }

        .progress-container {
          ::v-deep .el-progress-bar {
            padding-right: 0;
          }
        }
      }

      .card-actions {
        display: flex;
        justify-content: space-between;

        .action-buttons {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .card-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    min-height: 80px;
  }

  .card-left {
    flex: 1;
  }

  .card-right {
    margin-left: 20px;
    display: flex;
    align-items: center;
    height: 100%;
  }

  .warning-info {
    .warning-code {
      font-size: 16px;
      font-weight: 500;
      color: $title_text_color;
      margin-bottom: 8px;
    }

    .warning-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 8px;

      .batch-info {
        font-size: 14px;
        color: $value_color;
      }
    }

    .warning-meta {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 12px;

      .warning-time {
        font-size: 14px;
        color: $value_color;
      }

      .warning-level {
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;

        &.level-high {
          background: #FEF0F0;
          color: $red;
        }

        &.level-medium {
          background: #FDF6EC;
          color: $orange;
        }

        &.level-low {
          background: #F0F9FF;
          color: $blue;
        }
      }
    }

    .warning-detail {
      .detail-label {
        font-size: 14px;
        color: $label_color;
      }

      .detail-value {
        font-size: 14px;
        color: $value_color;
        margin-left: 4px;
      }

      .status-tag {
        margin-left: 12px;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;

        &.status-expired {
          background: #FEF0F0;
          color: $red;
        }

        &.status-expiring {
          background: #FDF6EC;
          color: $orange;
        }
      }



      .progress-container {
        margin-top: 8px;

        ::v-deep .el-progress-bar {
          padding-right: 0;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .warn-card {
    .card-content {
      flex-direction: column;
      align-items: stretch;
    }

    .card-right {
      margin-left: 0;
      margin-top: 16px;
      justify-content: flex-end;
    }

    .warning-info {
      .warning-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .warning-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }


    }
  }
}
</style>
