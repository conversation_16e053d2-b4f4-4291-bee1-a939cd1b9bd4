<template>
  <div class="warn-list">
    <div v-loading="loading" class="list-container">
      <div v-if="warningList.length > 0">
        <WarnCard
          v-for="item in warningList"
          :key="item.id"
          :data="item"
          :type="type"
        />
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <el-empty description="暂无预警数据"></el-empty>
      </div>
    </div>
    
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import WarnCard from './WarnCard.vue'
import { warningRecordPage } from '@/api/warning.js'

export default {
  name: 'WarnList',
  components: {
    WarnCard
  },
  props: {
    type: {
      type: String,
      required: true
    },
    severity: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      warningList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  watch: {
    type: {
      handler() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      immediate: true
    },
    severity() {
      this.queryParams.pageNum = 1
      this.getList()
    }
  },
  methods: {
    // 获取预警类型映射
    getWarningType() {
      const typeMap = {
        'lowStock': 1,   // 低库存预警
        'expiry': 2,     // 保质期预警
        'idleMat': 3     // 呆滞料预警
      }
      return typeMap[this.type] || 1
    },

    // 获取预警等级映射
    getWarningLevel() {
      const levelMap = {
        'high': 1,    // 高
        'medium': 2,  // 中
        'low': 3      // 低
      }
      return this.severity ? levelMap[this.severity] : null
    },

    // 获取预警列表
    async getList() {
      this.loading = true
      try {
        const params = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          warningType: this.getWarningType(),
          warningLevel: this.getWarningLevel()
        }

        const res = await warningRecordPage(params)

        if (res.success) {
          this.warningList = this.formatWarningList(res.result.list)
          this.total = parseInt(res.result.total)
        } else {
          this.$message.error(res.message || '获取预警列表失败')
        }

      } catch (error) {
        console.error('获取预警列表失败:', error)
        this.$message.error('获取预警列表失败')
      } finally {
        this.loading = false
      }
    },

    // 格式化预警列表数据
    formatWarningList(list) {
      return list.map(item => {
        const baseData = {
          id: item.warningRecordId,
          warningType: item.warningType,
          warningLevel: item.warningLevel,
          warningStatus: item.warningStatus,
          ignoreStatus: item.ignoreStatus,
          createTime: item.warningTime,
          level: this.getLevelText(item.warningLevel),
          levelText: this.getLevelDisplayText(item.warningLevel),
          actions: ['处理', '忽略']
        }

        // 根据预警类型添加特定字段
        if (item.warningType === 1) { // 低库存预警
          return {
            ...baseData,
            code: item.warehouseName || '未知仓库',
            currentStock:item.warehouseInventoryWeight,
            safeStock: item.warehouseSafetyStock,
            warehouseInventorySubNum: item.warehouseInventorySubNum
          }
        } else if (item.warningType === 2) { // 保质期预警
          return {
            ...baseData,
            code: item.materialCode || '未知物料',
            batch: item.batchNo || '未知批次',
            expiryDate: item.expiryDate || '未知',
            daysLeft: item.daysLeft || 0,
            status: item.daysLeft < 0 ? '已过期' : '即将过期'
          }
        } else if (item.warningType === 3) { // 呆滞料预警
          return {
            ...baseData,
            code: item.materialCode || '未知物料',
            count: item.inventoryQuantity || '0'
          }
        }

        return baseData
      })
    },

    // 获取等级文本
    getLevelText(level) {
      const levelMap = {
        1: 'high',
        2: 'medium',
        3: 'low'
      }
      return levelMap[level] || 'low'
    },

    // 获取等级显示文本
    getLevelDisplayText(level) {
      const levelMap = {
        1: '高',
        2: '中',
        3: '低'
      }
      return levelMap[level] || '-'
    }
  }
}
</script>

<style scoped lang='scss'>
.warn-list {
  .list-container {
    min-height: 400px;
  }

  .warning-cards {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }
}
</style>
