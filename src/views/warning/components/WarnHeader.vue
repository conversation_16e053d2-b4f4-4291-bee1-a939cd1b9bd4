<template>
  <el-card shadow="never" class="form_box">
    <el-row :gutter="20">
      <el-col :span="18">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="严重程度" prop="severity">
            <el-select
              v-model="queryParams.severity"
              placeholder="请选择严重程度"
              clearable
              @change="handleFilterChange"
              class="severity-select"
            >
              <el-option label="全部" value="">
                <span class="option-content">
                  <span>全部</span>
                  <span class="option-badge">8</span>
                </span>
              </el-option>
              <el-option label="严重" value="high">
                <span class="option-content">
                  <span>严重</span>
                  <span class="option-badge high">2</span>
                </span>
              </el-option>
              <el-option label="中等" value="medium">
                <span class="option-content">
                  <span>中等</span>
                  <span class="option-badge medium">3</span>
                </span>
              </el-option>
              <el-option label="轻微" value="low">
                <span class="option-content">
                  <span>轻微</span>
                  <span class="option-badge low">3</span>
                </span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="6" class="fend">
        <el-button
          type="primary"
          size="small"
          @click="handleSettings"
          class="settings-btn"
          icon="el-icon-setting"
        >
          预警设置
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-download"
          size="small"
          @click="handleExport"
          class="export-btn"
        >
          导出报表
        </el-button>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
export default {
  name: 'WarnHeader',
  props: {
    currentTab: {
      type: String,
      default: 'lowStock'
    },
    severity: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      queryParams: {
        severity: this.severity
      }
    }
  },
  watch: {
    severity(newVal) {
      this.queryParams.severity = newVal
    }
  },
  methods: {
    // 处理筛选条件改变
    handleFilterChange() {
      this.$emit('onFilterChange', this.queryParams.severity)
    },
    
    // 处理查询
    handleQuery() {
      this.handleFilterChange()
    },
    
    // 重置查询
    resetQuery() {
      this.queryParams.severity = ''
      this.handleFilterChange()
    },
    
    // 处理导出
    handleExport() {
      this.$emit('onExport')
    },
    
    // 处理设置
    handleSettings() {
      this.$emit('onSettings')
    }
  }
}
</script>

<style scoped lang='scss'>
@import '@/assets/css/theme.scss';

.fend {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}

.severity-select {
  ::v-deep .el-select-dropdown__item {
    padding: 0;

    .option-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding: 0 20px;

      .option-badge {
        background: #FFC700;
        color: #333;
        border-radius: 4px;
        padding: 2px 6px;
        font-size: 12px;
        min-width: 20px;
        text-align: center;

        &.high {
          background: #FF4D4F;
          color: white;
        }

        &.medium {
          background: #FFC700;
          color: #333;
        }

        &.low {
          background: #52C41A;
          color: white;
        }
      }
    }
  }
}

.settings-btn, .export-btn {
  height: 32px;
  border-radius: 4px;
  font-size: 14px;

  &.settings-btn {
    background: $theme_color;
    border-color: $theme_color;

    &:hover {
      background: lighten($theme_color, 10%);
      border-color: lighten($theme_color, 10%);
    }
  }

  &.export-btn {
    background: $theme_color;
    border-color: $theme_color;

    &:hover {
      background: lighten($theme_color, 10%);
      border-color: lighten($theme_color, 10%);
    }
  }
}
</style>
