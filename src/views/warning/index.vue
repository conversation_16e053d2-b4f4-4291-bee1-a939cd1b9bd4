<template>
  <div class="app-container">
    <WarnHeader
      :currentTab="activeTab"
      :severity="severity"
      @onFilterChange="handleFilterChange"
      @onExport="handleExport"
      @onSettings="handleSettings"
    />

    <!-- 预警标签 -->
    <WarnTabs
      :activeTab="activeTab"
      :severity="severity"
      @onTabChange="handleTabChange"
    />

    <!-- 预警设置 -->
    <el-drawer
      class="drawer_box"
      :visible.sync="settingDrawerVisible"
      :show-close="true"
      :append-to-body="true"
      :destroy-on-close="true"
      size="80%"
      title="预警设置"
      :wrapperClosable="false"
    >
      <WarningSetting @close="closeSettingDrawer" />
    </el-drawer>
  </div>
</template>

<script>
import WarnHeader from './components/WarnHeader.vue'
import WarnTabs from './components/WarnTabs.vue'
import WarningSetting from './warningSetting.vue'

export default {
  name: 'WarningManagement',
  components: {
    WarnHeader,
    WarnTabs,
    WarningSetting
  },
  data() {
    return {
      activeTab: 'lowStock',
      severity: '', // 严重等级
      settingDrawerVisible: false // 预警设置
    }
  },
  created() {
    // 初始化时可以设置默认值
  },
  methods: {
    // 处理筛选条件改变
    handleFilterChange(severity) {
      this.severity = severity
    },

    // 处理标签页切换
    handleTabChange(tab) {
      this.activeTab = tab
      // 切换tab时不重置严重程度，保持当前选择
    },

    // 处理导出报表
    async handleExport() {
      try {
        const tabNames = {
          lowStock: '低库存预警',
          expiry: '保质期预警',
          idleMat: '呆滞料预警'
        }

        console.log('导出' + tabNames[this.activeTab] + '报表')

        // const response = await exportWarningReport({
        //   type: this.activeTab,
        //   severity: this.severity
        // })

        this.$message.success(`${tabNames[this.activeTab]}报表导出成功`)
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请重试')
      }
    },

    // 处理预警设置
    handleSettings() {
      this.settingDrawerVisible = true
    },

    // 关闭预警设置抽屉
    closeSettingDrawer() {
      this.settingDrawerVisible = false
    }
  }
}
</script>

<style scoped lang='scss'>
.app-container {
  padding: 10px;
  background: #F7F8FA;
  min-height: calc(100vh - 84px);
}
</style>
